<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.botong.workorder.dao.WorkOrderProcessManageDao">

    <resultMap type="com.botong.workorder.entity.WorkOrderProcessManageViewEntity" id="workOrderProcessManageMap">
        <result property="workId" column="work_id"/>
        <result property="status" column="status"/>
        <result property="plantId" column="plant_id"/>
        <result property="upUserId" column="up_user_id"/>
        <result property="upUserName" column="up_user_name"/>
        <result property="upWorkTime" column="up_work_time"/>
        <result property="source" column="source"/>
        <result property="plantUserName" column="plant_user_name"/>
        <result property="address" column="address"/>
        <result property="inverterId" column="inverter_id"/>
        <result property="userPhone" column="user_phone"/>
        <result property="orgId" column="org_id"/>
        <result property="alarmInfo" column="alarm_info"/>
        <result property="alarmType" column="alarm_type"/>
        <result property="alarmDevice" column="alarm_device"/>
        <result property="warrantyContent" column="warranty_content"/>
        <result property="warrantyExpireDate" column="warranty_expire_date"/>
        <result property="alarmTime" column="alarm_time"/>
        <result property="alarmLevel" column="alarm_level"/>
        <result property="preRepairTime" column="pre_repair_time"/>
        <result property="remark" column="remark"/>
        <result property="remarkType" column="remark_type"/>
        <result property="repairStatus" column="repair_status"/>
        <result property="issuePhoto" column="issue_photo"/>
    </resultMap>
    <select id="selectSmsParamByWorkIds" resultType="map">
        SELECT
        work_apply_info.up_user_name AS dispatcher,
        work_base_info.id AS workId,
        work_base_info.alarm_time AS alarmTime,
        plant_base_info.address
        FROM
        work_base_info
        LEFT JOIN plant_base_info ON work_base_info.plant_id = plant_base_info.id
        LEFT JOIN work_apply_info ON work_base_info.id = work_apply_info.work_id
        <where>
            <if test="idList != null and idList.size() > 0">
                work_base_info.id IN
                <foreach collection="idList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            AND work_base_info.deleted = 0
            AND work_apply_info.deleted = 0
        </where>
    </select>
    <select id="getWorkIdWithRepairUserName" resultType="com.botong.workorder.vo.SysUserVO">
        SELECT t1.id,t2.work_id, t1.real_name
        FROM sys_user t1
        LEFT JOIN work_operation_person_info t2 ON t1.id = t2.repair_id
        <where>
            <if test="workIdList != null and workIdList.size() > 0">
                AND t2.work_id IN
                <foreach collection="workIdList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        AND t1.deleted = 0
        AND t2.deleted = 0
    </select>

    <select id="getWorkOrderStatistics" resultMap="workOrderStatisticsMap">
        SELECT ROUND((SUM(CASE WHEN status = 3 THEN 1 ELSE 0 END) / COUNT(*)) * 100, 2) AS closure_rate,
        ROUND((SUM(CASE WHEN status = 3 AND overtime = 0 THEN 1 ELSE 0 END) / COUNT(*)) * 100,
        2) AS timeliness_rate,
        ROUND(AVG(TIMESTAMPDIFF(HOUR, create_time, repair_time)), 2) AS avg_handling_time
        FROM work_order_process_manage_view
        <where>
            <if test="scopeList != null and scopeList.size() > 0">
                org_id IN
                <foreach collection="scopeList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="startTime != null and endTime != null ">
                <choose>
                    <when test="startTime.length() == 4">
                        AND DATE_FORMAT(create_time, '%Y') BETWEEN #startTime} AND #{endTime}
                    </when>
                    <when test="startTime.length() == 7">
                        AND DATE_FORMAT(create_time, '%Y-%m') BETWEEN #{startTime} AND #{endTime}
                    </when>
                    <when test="query.startTime.length() == 10">
                        AND DATE_FORMAT(create_time, '%Y-%m-%d') BETWEEN #{startTime} AND #{endTime}
                    </when>
                </choose>
            </if>
        </where>
    </select>

    <resultMap id="workOrderStatisticsMap" type="java.util.HashMap">
        <result property="closureRate" column="closure_rate"/>
        <result property="timelinessRate" column="timeliness_rate"/>
        <result property="avgHandlingTime" column="avg_handling_time"/>
    </resultMap>

    <select id="statisticsByAlarmTime" resultType="com.botong.workorder.vo.StatisticsByAlarmTimeVO">
        SELECT
        MONTH(alarm_time) AS month,
        COUNT(*) AS count
        FROM
        work_order_process_manage_view
        <where>
            YEAR(alarm_time) = #{year}
            <if test="plantId != null and plantId.trim() != ''">
                AND plant_id =#{plantId}
            </if>
        </where>

        GROUP BY
        YEAR(alarm_time), MONTH(alarm_time)
        ORDER BY
        month;
    </select>
    <select id="monthlyOpsWorkOrderSummary" resultType="com.botong.workorder.vo.MonthlyOpsWorkOrderSummaryVO">
        WITH w1 AS(
        SELECT city FROM plant_base_info GROUP BY city
        )
        ,w2 AS(
        SELECT dict_label,dict_value FROM sys_dict_data WHERE dict_type_id = 15 AND deleted = 0
        )
        ,w3 AS(
        SELECT * FROM w1 JOIN w2
        )
        ,w4 AS(
        SELECT
        city,
        alarm_type,
        count(*) AS `count`
        FROM
        work_order_process_manage_view
        <where>
            <if test="startTime != null and  endTime != null">
                create_time between #{startTime} AND #{endTime}
            </if>
        </where>
        GROUP BY
        city,
        alarm_type
        )
        SELECT
        w3.city,
        w3.dict_label alarmType,
        IFNULL(w4.count,0) count
        FROM
        w3 LEFT JOIN w4 ON w3.dict_value = w4.alarm_type AND w3.city = w4.city
        ORDER BY w3.city, dict_value
    </select>
    <select id="getPlantStatisticsInfo" resultType="com.botong.workorder.vo.CityStatsStatistics">
        SELECT city                                                                                           AS city,
               SUM(CASE WHEN plant_status IN (0, 2) THEN 1 ELSE 0 END)                                        AS total_alarms,
               SUM(CASE WHEN CAST(project_id AS CHAR) LIKE '4%' THEN 1 ELSE 0 END)                            AS rental_plant_total,
               SUM(CASE
                       WHEN CAST(project_id AS CHAR) LIKE '4%' AND plant_status IN (0, 2) THEN 1
                       ELSE 0 END)                                                                            AS rental_alarm_total,
               SUM(CASE WHEN CAST(project_id AS CHAR) LIKE '1%' THEN 1 ELSE 0 END)                            AS household_plant_total,
               SUM(CASE
                       WHEN CAST(project_id AS CHAR) LIKE '1%' AND DATE_ADD(create_time, INTERVAL 5 YEAR) &gt; NOW()
                           THEN 1
                       ELSE 0 END)                                                                            AS household_valid_total,
               SUM(CASE
                       WHEN CAST(project_id AS CHAR) LIKE '1%' AND plant_status IN (0, 2) AND
                            DATE_ADD(create_time, INTERVAL 5 YEAR) &gt; NOW() THEN 1
                       ELSE 0 END)                                                                            AS household_valid_alarms,
               SUM(CASE
                       WHEN CAST(project_id AS CHAR) LIKE '1%' AND DATE_ADD(create_time, INTERVAL 5 YEAR) &lt; NOW()
                           THEN 1
                       ELSE 0 END)                                                                            AS household_expired_total,
               SUM(CASE WHEN CAST(project_id AS CHAR) LIKE '2%' THEN 1 ELSE 0 END)                            AS county_plant_total,
               SUM(CASE
                       WHEN CAST(project_id AS CHAR) LIKE '2%' AND plant_status IN (0, 2) THEN 1
                       ELSE 0 END)                                                                            AS county_alarm_total,
               SUM(CASE WHEN CAST(project_id AS CHAR) LIKE '3%' THEN 1 ELSE 0 END)                            AS commercial_plant_total,
               SUM(CASE
                       WHEN CAST(project_id AS CHAR) LIKE '3%' AND plant_status IN (0, 2) THEN 1
                       ELSE 0 END)                                                                            AS commercial_alarm_total,
               SUM(CASE
                       WHEN CAST(project_id AS CHAR) LIKE '1%' AND plant_status IN (0, 2) AND
                            DATE_ADD(create_time, INTERVAL 5 YEAR) &lt;= NOW() THEN 1
                       ELSE 0 END)                                                                            AS household_expired_alarms,
               SUM(CASE
                       WHEN CAST(project_id AS CHAR) LIKE '1%' AND plant_status IN (0, 2) AND
                            DATE_ADD(create_time, INTERVAL 5 YEAR) &gt; NOW() AND
                            (plant_name LIKE '%（%' OR plant_name LIKE '%(%') THEN 1
                       ELSE 0 END)                                                                            AS household_owner_faults
        FROM plant_base_info
        GROUP BY city;
    </select>
    <select id="getAlarmStatisticsInfo" resultType="com.botong.workorder.vo.CityStatsStatistics">
        SELECT city                AS city,
               SUM(CASE
                       WHEN CAST(project_special AS CHAR) LIKE '4%' AND alarm_mean LIKE '%失压开关故障%' THEN 1
                       ELSE 0 END) AS low_voltage_faults,
               SUM(CASE
                       WHEN CAST(project_special AS CHAR) LIKE '4%' AND alarm_mean LIKE '%市电停电%' THEN 1
                       ELSE 0 END) AS power_outage,
               SUM(CASE
                       WHEN CAST(project_special AS CHAR) LIKE '4%' AND
                            (alarm_mean LIKE '%通讯异常%' OR alarm_mean LIKE '%通信异常%') THEN 1
                       ELSE 0 END) AS communication_faults
        FROM v_plant_alarm
        GROUP BY city;
    </select>
    <select id="getWorkOrderStatisticsInfo" resultType="com.botong.workorder.vo.CityStatsStatistics">
        SELECT city                                                                                             AS city,
               SUM(CASE WHEN DATE(up_work_time) = CURDATE() THEN 1 ELSE 0 END)                                  AS today_orders,
               SUM(CASE
                       WHEN DATE(up_work_time) = CURDATE() - INTERVAL 1 DAY THEN 1
                       ELSE 0 END)                                                                              AS yesterday_orders,
               SUM(CASE
                       WHEN `status` = 3 AND DATE(repair_time) = CURDATE() - INTERVAL 1 DAY THEN 1
                       ELSE 0 END)                                                                              AS yesterday_completed,
               SUM(CASE WHEN overtime = '1天' AND `status` != 3 THEN 1 ELSE 0 END)                              AS today_overtime
        FROM work_plant_info_view
        GROUP BY city;
    </select>
    <select id="getListByStatus" resultType="java.lang.String">
        SELECT
        plant_id
        FROM
        work_order_process_manage_view
        <where>
            <if test="statusList != null and statusList.size() &gt; 0">
                status IN
                <foreach collection="statusList" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
        </where>
    </select>
    <select id="getUserAlarmTypeStatistics" resultType="com.botong.workorder.vo.UserAlarmTypeStatisticsVO">
        WITH w1 AS (
        SELECT su.real_name AS username, sp.post_name AS post_name
        FROM sys_user su
        JOIN sys_user_post sup ON su.id = sup.user_id AND sup.deleted = 0
        JOIN sys_post sp ON sup.post_id = sp.id AND sp.deleted = 0
        WHERE su.deleted = 0
        GROUP BY su.id
        ),
        w2 AS (
        SELECT dict_label, dict_value
        FROM sys_dict_data
        WHERE dict_type_id = 15 AND deleted = 0
        ),
        w3 AS (
        SELECT * FROM w1 JOIN w2
        ),
        w4 AS (
        SELECT su.real_name AS username,wopmv.up_user_id,wopmv.org_id,
        COALESCE(wopmv.alarm_type, '') AS alarm_type,
        COUNT(*) AS `count`
        FROM work_order_process_manage_view wopmv
        JOIN work_operation_person_info wopi ON wopmv.work_id = wopi.work_id AND wopi.deleted = 0
        JOIN sys_user su ON (wopi.repair_id = su.id or wopmv.up_user_id = su.id) AND su.deleted = 0
        <where>
            <if test="query.startTime != '' and query.startTime != null and  query.endTime != '' and  query.endTime != null">
                wopmv.create_time between #{query.startTime} AND #{query.endTime}
            </if>
            <if test="scope != null and scope.size() > 0">
                AND (
                wopmv.org_id in
                <foreach collection="scope" open="(" close=")" separator="," item="id">
                    #{id}
                </foreach>
                <if test="creator != null">
                    OR wopmv.up_user_id = #{creator}
                </if>
                )
            </if>
        </where>
        GROUP BY su.real_name, wopmv.alarm_type
        order BY su.real_name
        )
        SELECT w3.username,w3.post_name, w3.dict_label AS alarmType, IFNULL(w4.count, 0) AS count
        FROM w3
        LEFT JOIN w4 ON w3.dict_value = w4.alarm_type AND w3.username = w4.username
        <where>


            <if test="query.username != null and  query.username.trim() != ''">
                and w3.username = #{query.username}
            </if>
        </where>
        ORDER BY w3.username, w3.dict_value;
    </select>
    <select id="getUnfinishedWorkOrderCount" resultType="map">
        SELECT
            city 城市,
            COUNT(*) 数量
        FROM
            work_order_process_manage_view
        WHERE
            `status` != 3
        GROUP BY
            city
    </select>
</mapper>