<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.botong.workorder.dao.WorkEvaluationScoreDao">

    <!-- 获取工程师基本信息 -->
    <select id="getEngineerInfo" resultType="map">
        SELECT DISTINCT
            su.id AS repairId,
            su.real_name AS repairName,
            su.city AS city
        FROM sys_user su
        <where>
            su.deleted = 0
            AND su.status = 1
            AND su.super_admin = 0
            <if test="query.repairId != null">
                AND su.id = #{query.repairId}
            </if>
            <if test="query.repairName != null and query.repairName.trim() != ''">
                AND su.real_name LIKE CONCAT('%', #{query.repairName}, '%')
            </if>
            <if test="query.city != null and query.city.trim() != ''">
                AND su.city = #{query.city}
            </if>
        </where>
        ORDER BY su.real_name
    </select>

    <!-- 获取所有报警类型字典数据 -->
    <select id="getAllAlarmTypes" resultType="map">
        SELECT
            dict_value AS alarmType,
            dict_label AS alarmTypeName,
            CAST(sort AS SIGNED) AS sort
        FROM sys_dict_data
        WHERE dict_type_id = 15
        AND deleted = 0

        UNION ALL

        SELECT
            NULL AS alarmType,
            '未统计' AS alarmTypeName,
            CAST(999 AS SIGNED) AS sort

        ORDER BY sort ASC, alarmType ASC
    </select>

    <!-- 获取工程师扁平化评分数据 -->
    <select id="getEngineerFlatScores" resultType="map">
        SELECT
            wopi.repair_id AS repairId,
            su.real_name AS name,
            wopmv.city AS city,
            wopmv.alarm_type AS alarmType,
            CASE
                WHEN sdd.dict_label IS NOT NULL AND sdd.dict_label != '' THEN sdd.dict_label
                ELSE '未统计'
            END AS alarmTypeName,
            COUNT(*) AS count,
            COALESCE(wec.score, 0) AS coefficient,
            (COUNT(*) * COALESCE(wec.score, 0)) AS subtotal
        FROM work_order_process_manage_view wopmv
        INNER JOIN work_operation_person_info wopi ON wopmv.work_id = wopi.work_id AND wopi.deleted = 0
        INNER JOIN sys_user su ON wopi.repair_id = su.id AND su.deleted = 0 AND su.super_admin = 0
        LEFT JOIN work_evaluat_coefficient wec ON wopmv.alarm_type = wec.type AND wopmv.city = wec.city AND wec.deleted = 0
        LEFT JOIN sys_dict_data sdd ON wopmv.alarm_type = sdd.dict_value AND sdd.dict_type_id = 15 AND sdd.deleted = 0
        <where>
            wopmv.city IS NOT NULL
            <if test="query.startTime != null and query.endTime != null">
                AND DATE(wopmv.create_time) BETWEEN DATE(#{query.startTime}) AND DATE(#{query.endTime})
            </if>
            <if test="query.repairId != null">
                AND wopi.repair_id = #{query.repairId}
            </if>
            <if test="query.repairName != null and query.repairName.trim() != ''">
                AND su.real_name LIKE CONCAT('%', #{query.repairName}, '%')
            </if>
            <if test="query.city != null and query.city.trim() != ''">
                AND wopmv.city = #{query.city}
            </if>
        </where>
        GROUP BY wopi.repair_id, su.real_name, wopmv.city, wopmv.alarm_type,
                 CASE
                     WHEN sdd.dict_label IS NOT NULL AND sdd.dict_label != '' THEN sdd.dict_label
                     ELSE '未统计'
                 END,
                 COALESCE(wec.score, 0)
        ORDER BY su.real_name, wopmv.alarm_type
    </select>

</mapper>
