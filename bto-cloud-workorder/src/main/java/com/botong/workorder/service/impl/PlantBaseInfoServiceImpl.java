package com.botong.workorder.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.botong.api.module.photovoltaic.PhotovoltaicApi;
import com.botong.api.module.photovoltaic.vo.PlantAlarmVO;
import com.botong.api.module.photovoltaic.vo.ProjectTypeTreeVO;
import com.botong.api.module.system.SysDictDataApi;
import com.botong.api.module.system.SysOrgApi;
import com.botong.api.module.system.vo.DictDataVO;
import com.botong.api.module.utlis.RequestPhotovoltaicUtil;
import com.botong.api.module.workorder.entity.OrgBaseEntity;
import com.botong.framework.common.constant.BtoConstant;
import com.botong.framework.common.exception.ServerException;
import com.botong.framework.common.page.PageResult;
import com.botong.framework.common.utils.BusinessCalculateUtil;
import com.botong.framework.common.utils.ExcelUtils;
import com.botong.framework.common.utils.JsonUtils;
import com.botong.framework.mybatis.interceptor.DataScope;
import com.botong.framework.mybatis.service.impl.BaseServiceImpl;
import com.botong.workorder.convert.PlantBaseInfoConvert;
import com.botong.workorder.dao.PlantBaseInfoDao;
import com.botong.workorder.entity.PlantBaseInfoEntity;
import com.botong.workorder.entity.WorkOrderProcessManageViewEntity;
import com.botong.workorder.query.LowPowerQuery;
import com.botong.workorder.query.PlantAlarmQuery;
import com.botong.workorder.query.PlantBaseInfoQuery;
import com.botong.workorder.service.PlantAlarmService;
import com.botong.workorder.service.PlantBaseInfoService;
import com.botong.workorder.service.WorkBaseInfoService;
import com.botong.workorder.vo.LowPowerPlantVO;
import com.botong.workorder.vo.PlantBaseInfoVO;
import com.botong.workorder.vo.SiteDiagramVO;
import com.botong.workorder.vo.SiteDiagramYxVO;
import com.fhs.trans.service.impl.TransService;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> by zhb on 2023/8/16.
 */
@Slf4j
@Service
@AllArgsConstructor
public class PlantBaseInfoServiceImpl extends BaseServiceImpl<PlantBaseInfoDao, PlantBaseInfoEntity> implements PlantBaseInfoService {

    private final PhotovoltaicApi photovoltaicApi;
    private final SysDictDataApi sysDictDataApi;
    private final SysOrgApi sysOrgApi;
    private final WorkBaseInfoService workBaseInfoService;
    private final PlantAlarmService playAlarmService;
    private final PlantBaseInfoDao plantBaseInfoDao;
    private final TransService transService;

    @Override
    public void exportLowPowerPlant(LowPowerQuery query) {
        query.setLimit(-1);
        query.setPage(-1);
        PageResult<LowPowerPlantVO> res = this.getLowPowerPlant(query);
        transService.transBatch(res.getList());
        ExcelUtils.excelExport(LowPowerPlantVO.class, "低电量电站统计" + query.getStartTime() + "-" + query.getEndTime() + "_" + System.currentTimeMillis(), null, res.getList());

    }

    @Override
    public PageResult<LowPowerPlantVO> getLowPowerPlant(LowPowerQuery query) {
        // 根据机构id查询出光伏项目id及子id
        // List<String> scope = plantBaseInfoDao.getOrgIdByProjectIds(10L);
        Page<LowPowerPlantVO> page = new Page<>(query.getPage(), query.getLimit());

        List<String> orgIds = null;
        // 工单机构id 转-->光云id
        if (CollUtil.isNotEmpty(query.getProjectId())) {
            HashMap<String, Object> params = new HashMap<>();

            List<Long> scopeList = CollUtil.isNotEmpty(query.getProjectId()) ? sysOrgApi.getSubOrgIdList(query.getProjectId()) : Collections.emptyList();
            params.put("orgIds", scopeList);
            orgIds = baseMapper.getPlantTypeByOrgId(params);
            if (CollUtil.isEmpty(orgIds)) {
                return new PageResult<>(Collections.emptyList(), 0);
            }
        }

        Page<LowPowerPlantVO> res = baseMapper.getLowPowerPlant(null, orgIds, page, query);


        String plantType = BtoConstant.DICT_PLANT_TYPE_KEY;
        List<DictDataVO> dictDataVos = sysDictDataApi.getDictSqlByType(plantType);

        for (LowPowerPlantVO item : res.getRecords()) {
            item.setPlantCapacity(BusinessCalculateUtil.getRealPlantCapacity(item.getPlantCapacity()));
            if (item.getProjectSpecial() != null) {
                dictDataVos.forEach(dictDataVo -> {
                    // 项目类型转换，光云系统projectId-->工单系统orgId
                    if (dictDataVo.getDictValue().equals(item.getProjectSpecial())) {
                        Long orgId = sysOrgApi.getOrgIdByName(dictDataVo.getDictLabel());
                        item.setProjectSpecial(String.valueOf(orgId));
                        item.setProjectName(dictDataVo.getDictLabel());
                    }
                });
            }
        }


        return new PageResult<>(res.getRecords(), res.getTotal());
    }

    @Override
    public HashMap<String, Object> getPlantDiagram(String plantId, Integer plantType) {
        // 获取指定plantId的合同ID
        String contractId = plantBaseInfoDao.getContractIdByPlantId(plantId);
        // 如果合同ID为空或空白，返回null
        if (StrUtil.isBlank(contractId)) {
            return null;
        }

        // 获取类型为"yx_plant_type"的字典数据
        List<DictDataVO> yxPlantType = sysDictDataApi.getDictDataByTypeId("yx_plant_type");
        // 将字典数据中的dictValue转换为Long列表
        List<Long> yxProject = yxPlantType.stream()
                .map(item -> Long.parseLong(item.getDictValue()))
                .collect(Collectors.toList());
        // 获取子组织ID列表
        List<Long> yx = sysOrgApi.getSubOrgIdList(yxProject);

        // 初始化电气图和现场图的表名
        String electricalTable = "v_electrical_diagram";
        String siteTable = "v_site_diagram";

        // 判断是否为yx类型的项目
        Boolean isYx = CollUtil.isNotEmpty(yx) && yx.contains(plantType.longValue());
        if (isYx) {
            electricalTable = "v_electrical_diagram_yx";
            siteTable = "v_site_diagram_yx";
        }

        // 创建结果Map
        HashMap<String, Object> res = new HashMap<>();

        // 获取电气图数据
        List<String> elJson = plantBaseInfoDao.getElectricalDiagram(electricalTable, contractId);

        if (isYx) {
            // 如果是yx类型项目，将电气图数据转换为List<String>
            if (CollUtil.isEmpty(elJson)) {
                res.put("electrical", elJson);
            } else {
                List<String> elList = JSONUtil.toList(elJson.get(0), String.class)
                        .stream()
                        .map(str -> str.replace("http://", "https://"))
                        .collect(Collectors.toList());
                res.put("electrical", elList);
            }


            // 获取现场图数据
            List<String> site = plantBaseInfoDao.getSiteDiagramYx(siteTable, contractId);

            if (CollUtil.isNotEmpty(site)) {
                site = site
                        .stream()
                        .map(str -> str.replace("http://", "https://"))
                        .collect(Collectors.toList());
                // 获取第一个现场图数据
                String yxSite = site.get(0);

                // 解析 JSON
                JSONArray jsonArray = JSONUtil.parseArray(yxSite);

                List<SiteDiagramYxVO> normalizedList = new ArrayList<>();

                for (Object obj : jsonArray) {
                    JSONObject jsonObj = JSONUtil.parseObj(obj);

                    // 封装 SiteDiagramYxVO
                    SiteDiagramYxVO siteDiagram = new SiteDiagramYxVO();
                    siteDiagram.setImageName(jsonObj.getStr("imageName"));

                    JSONArray imageListJson = jsonObj.getJSONArray("imageList");
                    ArrayList<SiteDiagramYxVO.ImageList> imageList = new ArrayList<>();

                    if (CollUtil.isNotEmpty(imageListJson)) {
                        // 处理标准格式 JSON1 存在imageList
                        for (Object imgObj : imageListJson) {
                            JSONObject imgJson = JSONUtil.parseObj(imgObj);
                            SiteDiagramYxVO.ImageList img = new SiteDiagramYxVO.ImageList();
                            img.setImageAddr(imgJson.getStr("imageAddr"));
                            img.setImageName(imgJson.getStr("imageName"));
                            imageList.add(img);
                        }
                    } else {
                        // 处理非标准格式 JSON2 不存在imageList
                        if (StrUtil.isNotBlank(jsonObj.getStr("imageAddr"))) {
                            SiteDiagramYxVO.ImageList img = new SiteDiagramYxVO.ImageList();
                            img.setImageAddr(jsonObj.getStr("imageAddr"));
                            img.setImageName(jsonObj.getStr("imageName"));
                            imageList.add(img);
                        }
                    }

                    siteDiagram.setImageList(imageList);
                    normalizedList.add(siteDiagram);
                }

                // 过滤现场图数据，只保留特定的图片名称
                normalizedList = normalizedList.stream()
                        .filter(item -> "逆变器和配电箱整体照".equals(item.getImageName()) || "配电箱内部照".equals(item.getImageName()))
                        .collect(Collectors.toList());

                res.put("site", normalizedList);
            } else {
                res.put("site", null);
            }
        } else {
            // 如果不是yx类型项目，将电气图数据作为单元素列表放入结果Map
            List<String> list = elJson.stream()
                    .map(str -> str.replace("http://", "https://"))
                    .collect(Collectors.toList());
            res.put("electrical", list);

            // 获取现场图数据
            List<SiteDiagramVO> siteDiagram = plantBaseInfoDao.getSiteDiagram(siteTable, contractId);
            List<SiteDiagramYxVO> vos = new ArrayList<>();
            // 将SiteDiagramVO转换为SiteDiagramYxVO
            siteDiagram.forEach(item -> {
                SiteDiagramYxVO vo = new SiteDiagramYxVO();
                vo.setImageName(item.getFileName());
                SiteDiagramYxVO.ImageList imageList = new SiteDiagramYxVO.ImageList();
                imageList.setImageName(item.getFileName());
                imageList.setImageAddr(item.getFileAddr());
                vo.setImageList(CollUtil.toList(imageList));
                vos.add(vo);
            });

            res.put("site", vos);
        }

        // 返回结果Map
        return res;
    }


    @Override
    public List<String> transOrgId(List<Long> orgIds) {
        return baseMapper.transOrgId(orgIds);
    }

    @Override
    public HashMap<String, Object> getPlantNumInfo(List<Long> scopeList) {
        // 工单id 转光云id
        List<String> newScope = transOrgId(scopeList);
        QueryWrapper<PlantBaseInfoEntity> wrapper = new QueryWrapper<>();


        wrapper.select("plant_status status, count(*) statusNum");
        wrapper.lambda()
                .isNotNull(PlantBaseInfoEntity::getId)
                .in(CollUtil.isNotEmpty(newScope), PlantBaseInfoEntity::getProjectId, newScope);
        getWorkOrderDataScopeWrapper(wrapper.lambda(), null, "project_id", "id");
        wrapper.lambda().groupBy(PlantBaseInfoEntity::getPlantStatus);
        List<Map<String, Object>> statusMap = this.listMaps(wrapper);


        wrapper.clear();
        wrapper.select("count(*) total, sum(plant_capacity) totalCapacity")
                .lambda().in(CollUtil.isNotEmpty(newScope), PlantBaseInfoEntity::getProjectId, newScope);
        getWorkOrderDataScopeWrapper(wrapper.lambda(), null, "project_id", "id");
        Map<String, Object> statisticMap = this.getMap(wrapper);

        if (CollUtil.isNotEmpty(statisticMap)) {
            statisticMap.put("totalPlant", statisticMap.get("total").toString());
            statisticMap.put("totalCapacity", BusinessCalculateUtil.getRealPlantCapacity(statisticMap.get("totalCapacity") == null ? "" : statisticMap.get("totalCapacity").toString()));
        }

        HashMap<String, Object> result = new HashMap<>();
        result.put("plantStatistic", statisticMap);
        result.put("plantStatusStatistic", statusMap);
        return result;
    }

    @Override
    public List<OrgBaseEntity> getAllProject() {
        return plantBaseInfoDao.getAllProject();
    }

    @Override
    public void updateProjectByName(String redisName, String name) {
        plantBaseInfoDao.updateProjectByName(redisName, name);
    }

    @Override
    public PageResult<PlantBaseInfoVO> page(PlantBaseInfoQuery query) {
        IPage<PlantBaseInfoEntity> page = baseMapper.selectPage(getPage(query), getWrapper(query));
        return new PageResult<>(PlantBaseInfoConvert.INSTANCE.convertList(page.getRecords()), page.getTotal());
    }

    private LambdaQueryWrapper<PlantBaseInfoEntity> getWrapper(PlantBaseInfoQuery query) {
        LambdaQueryWrapper<PlantBaseInfoEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(query.getPlatform() != null, PlantBaseInfoEntity::getPlatform, query.getPlatform());
        return wrapper;
    }

    @Override
    public PlantBaseInfoEntity save(PlantBaseInfoVO vo) {
        PlantBaseInfoEntity entity = PlantBaseInfoConvert.INSTANCE.convert(vo);

        baseMapper.insert(entity);
        return entity;
    }

    @Override
    public void update(PlantBaseInfoVO vo) {
        PlantBaseInfoEntity entity = PlantBaseInfoConvert.INSTANCE.convert(vo);

        updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<Long> idList) {
        removeByIds(idList);
    }

    @Override
    public Map<String, List<Map<String, Object>>> getPlantIdsAndNames(@Valid Integer platform) {
        HashMap<String, List<Map<String, Object>>> hashMap = new HashMap<>();
        QueryWrapper<PlantBaseInfoEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("id as plantId");
        queryWrapper.lambda().eq(platform != null, PlantBaseInfoEntity::getPlatform, platform);
        List<Map<String, Object>> ids = baseMapper.selectMaps(queryWrapper);
        hashMap.put("ids", ids);
        queryWrapper.clear();
        queryWrapper.lambda().select(PlantBaseInfoEntity::getPlantName);
        queryWrapper.lambda().eq(platform != null, PlantBaseInfoEntity::getPlatform, platform);
        List<Map<String, Object>> names = baseMapper.selectMaps(queryWrapper);
        hashMap.put("names", names);
        return hashMap;
    }

    @Override
    public PlantBaseInfoVO getInfoByIdOrName(String param, Integer platform) {
        // 根据ID查询是否存在电站
        PlantBaseInfoEntity plantBaseInfoEntity = baseMapper.selectById(param);
        String plantType = BtoConstant.DICT_PLANT_TYPE_KEY;
        List<DictDataVO> dictDataVos = sysDictDataApi.getDictSqlByType(plantType);
        MPJLambdaWrapper<PlantBaseInfoEntity> queryWrapper = new MPJLambdaWrapper<>();
        PlantBaseInfoVO plantBaseInfoVO;
        // 查询结果存在且平台类型相等则返回
        if (plantBaseInfoEntity != null && plantBaseInfoEntity.getPlatform().equals(platform)) {
            plantBaseInfoVO = PlantBaseInfoConvert.INSTANCE.convert(plantBaseInfoEntity);
        } else if (plantBaseInfoEntity != null) {
            // 平台类型不相等，根据平台类型和ID查询
            queryWrapper.eq(PlantBaseInfoEntity::getPlatform, platform);
            queryWrapper.eq(PlantBaseInfoEntity::getId, param);
            plantBaseInfoVO = PlantBaseInfoConvert.INSTANCE.convert(baseMapper.selectOne(queryWrapper));
        } else {
            // 根据平台和电站用户进行条件查询
            queryWrapper.eq(PlantBaseInfoEntity::getPlatform, platform);
            queryWrapper.eq(PlantBaseInfoEntity::getPlantName, param).or().eq(PlantBaseInfoEntity::getPlantUserName, param);
            plantBaseInfoEntity = baseMapper.selectOne(queryWrapper);
            plantBaseInfoVO = PlantBaseInfoConvert.INSTANCE.convert(plantBaseInfoEntity);
        }
        // 查询告警信息
        plantBaseInfoVO.setAlarmStr(baseMapper.getAlarmStr(plantBaseInfoVO.getPlantId()));
        for (DictDataVO dictDataVo : dictDataVos) {
            if (Objects.nonNull(plantBaseInfoVO) && plantBaseInfoVO.getProjectId() != null) {
                // 项目类型转换，光云系统projectId-->工单系统orgId
                String projectId = plantBaseInfoVO.getProjectId().toString();
                if (dictDataVo.getDictValue().equals(projectId)) {
                    Long orgId = sysOrgApi.getOrgIdByName(dictDataVo.getDictLabel());
                    if (orgId == 0L) {
                        char firstChar = projectId.charAt(0);
                        int firstDigit = Character.getNumericValue(firstChar);
                        String description = (firstDigit == 1) ? "传统户用" : (firstDigit == 2) ? "整县" : (firstDigit == 3) ? "工商业" : (firstDigit == 4) ? "户租" : (firstDigit == 5) ? "产品服务" : "非光云平台";
                        orgId = sysOrgApi.getOrgIdByName(description);
                    }
                    plantBaseInfoVO.setPlantType(orgId);
                }
            }
        }
        return plantBaseInfoVO;
    }

    @Override
    public PageResult<PlantBaseInfoVO> getInfoLikeIdOrName(@Valid PlantBaseInfoQuery query) {
        MPJLambdaWrapper<PlantBaseInfoEntity> wrapper = new MPJLambdaWrapper<>();

        wrapper.in(CollUtil.isNotEmpty(query.getPlantStatus()), PlantBaseInfoEntity::getPlantStatus, query.getPlantStatus());

        List<String> orgIds = null;
        // 工单机构id 转-->光云id
        if (CollUtil.isNotEmpty(query.getProjectId())) {
            DataScope dataScope = getWorkOrderDataScope(null, "project_id", "id");
            HashMap<String, Object> params = new HashMap<>();
            params.put("dataScope", dataScope);

            List<Long> scopeList = CollUtil.isNotEmpty(query.getProjectId()) ? sysOrgApi.getSubOrgIdList(query.getProjectId()) : Collections.emptyList();
            params.put("orgIds", scopeList);
            orgIds = baseMapper.getPlantTypeByOrgId(params);
        }
        wrapper.in(CollUtil.isNotEmpty(orgIds), PlantBaseInfoEntity::getProjectId, orgIds);

        if (StrUtil.isNotBlank(query.getParam())) {
            wrapper.and(w -> w.like(PlantBaseInfoEntity::getPlantName, query.getParam()).or().like(PlantBaseInfoEntity::getId, query.getParam()));
        }
        wrapper.eq(query.getPlatform() != null, PlantBaseInfoEntity::getPlatform, query.getPlatform());
        wrapper.selectAll(PlantBaseInfoEntity.class)
                .select("MAX(CASE WHEN t1.status != 3 THEN true ELSE false END) AS existWorkOrder")
                .leftJoin(WorkOrderProcessManageViewEntity.class, WorkOrderProcessManageViewEntity::getPlantId, PlantBaseInfoEntity::getId)
                .groupBy(PlantBaseInfoEntity::getId);
        IPage<PlantBaseInfoEntity> page = baseMapper.selectPage(getPage(query), wrapper);
        List<PlantBaseInfoVO> plantBaseInfoVos = PlantBaseInfoConvert.INSTANCE.convertList(page.getRecords());
        String plantType = BtoConstant.DICT_PLANT_TYPE_KEY;
        List<DictDataVO> dictDataVos = sysDictDataApi.getDictSqlByType(plantType);

        plantBaseInfoVos.forEach(plantBaseInfoVO -> {
                    if (plantBaseInfoVO.getProjectId() != null) {
                        dictDataVos.forEach(dictDataVo -> {
                            // 项目类型转换，光云系统projectId-->工单系统orgId
                            if (dictDataVo.getDictValue().equals(plantBaseInfoVO.getProjectId().toString())) {
                                Long orgId = sysOrgApi.getOrgIdByName(dictDataVo.getDictLabel());
                                plantBaseInfoVO.setPlantType(orgId);
                                plantBaseInfoVO.setProjectName(dictDataVo.getDictLabel());
                            }
                        });
                    }
                }
        );
        return new PageResult<>(plantBaseInfoVos, page.getTotal());

    }

    @Override
    public PageResult<PlantAlarmVO> requestAlarmList(PlantAlarmQuery query) {
        try {
            // 根据机构id查询出光伏项目id及子id
            List<String> projectIds = plantBaseInfoDao.getOrgIdByProjectIds(query.getPlantType());
            query.setProjectSpecialList(projectIds);
            // 分页查询
            Page<PlantAlarmVO> page = new Page<>(query.getPage(), query.getLimit());
            IPage<PlantAlarmVO> pageList = playAlarmService.getPageList(query, page);
            List<PlantAlarmVO> alarmVos = pageList.getRecords();
            Date now = DateUtil.date();
            for (PlantAlarmVO alarmVo : alarmVos) {
                //获取告警开始时间和当前时间比对，不足48小时按小时计算，大于48小时按天计算
                Date startTime = alarmVo.getStartTime();
                long hours = DateUtil.between(startTime, now, DateUnit.HOUR);
                if (hours < 48) {
                    alarmVo.setAlarmDuration(hours + "小时");
                } else {
                    long days = DateUtil.betweenDay(startTime, now, true);
                    alarmVo.setAlarmDuration(days + "天");
                }
            }
            return new PageResult<>(alarmVos, page.getTotal());
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return null;
    }

    @Override
    public List<ProjectTypeTreeVO> getProjectTypeTree() {
        Object result = photovoltaicApi.getProjectTypeTree();
        Object data = RequestPhotovoltaicUtil.getRequestData(result);
        if (Objects.isNull(data)) {
            throw new ServerException("光伏系统数据转换异常");
        }
        return JsonUtils.parseArray(data.toString(), ProjectTypeTreeVO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrUpdate(PlantBaseInfoEntity entity) {
        // 创建一个查询条件，根据plantId和plantName进行查询
        LambdaQueryWrapper<PlantBaseInfoEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StrUtil.isNotBlank(entity.getId()), PlantBaseInfoEntity::getId, entity.getId());

        // 根据查询条件判断是否存在记录
        boolean idExist = baseMapper.exists(wrapper);

        if (Boolean.TRUE.equals(idExist)) {
            // 判断plantName是否已存在
            getNameExist(entity, wrapper);
            // 根据plantId和plantName进行更新
            return updateById(entity);
        } else {
            // 判断plantName是否已存在
            getNameExist(entity, wrapper);
            // 根据plantId和plantName进行新增
            return save(entity);
        }
    }

    private void getNameExist(PlantBaseInfoEntity entity, LambdaQueryWrapper<PlantBaseInfoEntity> wrapper) {
        // 清除wrapper中的条件
        wrapper.clear();
        // 判断entity中的id和plantName是否为空，不为空则添加到wrapper中
        wrapper.ne(StrUtil.isNotBlank(entity.getId()), PlantBaseInfoEntity::getId, entity.getId())
                .eq(StrUtil.isNotBlank(entity.getPlantName()), PlantBaseInfoEntity::getPlantName, entity.getPlantName());
        // 判断wrapper中是否存在满足条件的实体
        boolean nameExist = baseMapper.exists(wrapper);
        // 如果存在，则抛出异常
        if (nameExist) {
            throw new ServerException("电站名'" + entity.getPlantName() + "'已存在");
        }
    }

    @Override
    public PlantBaseInfoEntity getByInfo(Integer platform, String plantId, String plantName) {
        LambdaQueryWrapper<PlantBaseInfoEntity> wrapper = new LambdaQueryWrapper<>();

        // 查询电站信息
        wrapper.eq(platform != null, PlantBaseInfoEntity::getPlatform, platform);

        wrapper.and(w -> {
            w.eq(StrUtil.isNotBlank(plantId), PlantBaseInfoEntity::getId, plantId)
                    .or()
                    .eq(StrUtil.isNotBlank(plantName), PlantBaseInfoEntity::getPlantName, plantName);
        });
        return this.getOne(wrapper);
    }
}