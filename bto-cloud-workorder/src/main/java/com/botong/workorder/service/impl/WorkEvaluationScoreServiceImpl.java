package com.botong.workorder.service.impl;

import cn.hutool.core.util.URLUtil;
import com.alibaba.excel.EasyExcel;
import com.botong.framework.common.utils.HttpContextUtils;
import com.botong.workorder.dao.WorkEvaluationScoreDao;
import com.botong.workorder.query.WorkEvaluationScoreQuery;
import com.botong.workorder.service.WorkEvaluationScoreService;
import com.botong.workorder.vo.WorkEvaluationFlatScoreVO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 工作订单评估评分服务实现
 *
 * <AUTHOR> 
 * @since 1.0.0 2025-07-04
 */
@Slf4j
@Service
@AllArgsConstructor
public class WorkEvaluationScoreServiceImpl implements WorkEvaluationScoreService {
    
    private final WorkEvaluationScoreDao workEvaluationScoreDao;
    
    @Override
    public WorkEvaluationFlatScoreVO getFlatEvaluationScores(WorkEvaluationScoreQuery query) {
        // 1. 获取所有报警类型字典数据
        List<Map<String, Object>> alarmTypes = workEvaluationScoreDao.getAllAlarmTypes();

        // 2. 获取工程师扁平化评分数据
        List<Map<String, Object>> flatScores = workEvaluationScoreDao.getEngineerFlatScores(query);

        // 3. 构建报警类型字段列表（按排序）
        List<String> alarmTypeFields = alarmTypes.stream()
                .sorted((a, b) -> {
                    // 安全地处理 sort 字段的类型转换，支持 Integer 和 Long
                    Number sortA = (Number) a.get("sort");
                    Number sortB = (Number) b.get("sort");
                    if (sortA != null && sortB != null) {
                        return Integer.compare(sortA.intValue(), sortB.intValue());
                    }
                    return 0;
                })
                .map(type -> (String) type.get("alarmTypeName"))
                .collect(Collectors.toList());

        // 4. 按工程师分组处理数据
        Map<String, Map<String, Object>> engineerMap = new LinkedHashMap<>();

        for (Map<String, Object> score : flatScores) {
            Long repairId = (Long) score.get("repairId");
            String name = (String) score.get("name");
            String city = (String) score.get("city");
            String alarmTypeName = (String) score.get("alarmTypeName");
            Integer count = ((Number) score.get("count")).intValue();
            BigDecimal subtotal = (BigDecimal) score.get("subtotal");

            String engineerKey = repairId + "_" + name + "_" + city;

            Map<String, Object> engineerData = engineerMap.computeIfAbsent(engineerKey, k -> {
                Map<String, Object> data = new LinkedHashMap<>();
                data.put("姓名", name);
                data.put("城市", city);
                data.put("总工单数", 0);
                data.put("总得分", BigDecimal.ZERO);

                // 按顺序初始化所有报警类型字段为0
                for (String fieldName : alarmTypeFields) {
                    data.put(fieldName, 0);
                }
                return data;
            });

            // 更新报警类型数量
            if (alarmTypeName != null && !alarmTypeName.trim().isEmpty()) {
                engineerData.put(alarmTypeName, count);
            }

            // 累加总工单数和总得分
            Integer currentTotal = (Integer) engineerData.get("总工单数");
            BigDecimal currentScore = (BigDecimal) engineerData.get("总得分");

            engineerData.put("总工单数", currentTotal + count);
            engineerData.put("总得分", currentScore.add(subtotal));
        }

        // 5. 确保包含所有符合条件的工程师（即使没有处理工单）
        List<Map<String, Object>> engineerInfos = workEvaluationScoreDao.getEngineerInfo(query);
        for (Map<String, Object> engineerInfo : engineerInfos) {
            String name = (String) engineerInfo.get("repairName");
            Long repairId = (Long) engineerInfo.get("repairId");
            String engineerCity = (String) engineerInfo.get("city");
            String engineerKey = repairId + "_" + name + "_" + engineerCity;

            // 如果该工程师还没有数据，创建一个空记录
            if (!engineerMap.containsKey(engineerKey)) {
                Map<String, Object> data = new LinkedHashMap<>();
                data.put("姓名", name);
                data.put("城市", engineerCity != null ? engineerCity : "");
                data.put("总工单数", 0);
                data.put("总得分", BigDecimal.ZERO);

                // 初始化所有报警类型字段为0
                for (String fieldName : alarmTypeFields) {
                    data.put(fieldName, 0);
                }

                engineerMap.put(engineerKey, data);
            }
        }

        // 6. 转换为列表并排序
        List<Map<String, Object>> engineerScores = new ArrayList<>(engineerMap.values());
        engineerScores.sort((a, b) -> {
            String nameA = (String) a.get("姓名");
            String nameB = (String) b.get("姓名");
            return nameA.compareTo(nameB);
        });

        // 对engineerScores进行过滤，如果总工单数为0则不要
        engineerScores = engineerScores.stream().filter(engineerScore -> {
            Integer totalWorkOrders = (Integer) engineerScore.get("总工单数");
            return totalWorkOrders != null && totalWorkOrders > 0;
        }).collect(Collectors.toList());

        // 7. 构建响应对象
        WorkEvaluationFlatScoreVO result = new WorkEvaluationFlatScoreVO();
        result.setStartTime(query.getStartTime());
        result.setEndTime(query.getEndTime());
        result.setAlarmTypeFields(alarmTypeFields);
        result.setEngineerScores(engineerScores);
        result.setTotalRecords((long) engineerScores.size());

        return result;
    }

    @Override
    public void exportFlatEvaluationScores(WorkEvaluationScoreQuery query) {
        try {
            log.info("开始导出工程师评估评分数据，查询条件: {}", query);
            log.info("分页参数 - Page: {}, Limit: {}", query.getPage(), query.getLimit());

            // 1. 获取扁平化评分数据（应该已经设置了 page=-1, limit=-1）
            WorkEvaluationFlatScoreVO result = getFlatEvaluationScores(query);

            // 2. 检查数据是否为空
            if (result == null || result.getEngineerScores() == null || result.getEngineerScores().isEmpty()) {
                log.warn("查询结果为空，将导出空的Excel文件");
                // 创建空数据但包含表头的Excel
                List<Map<String, Object>> emptyData = createEmptyDataWithHeaders(result);
                String fileName = generateFileName();
                exportToExcelWithDynamicHeaders(emptyData, fileName,
                    result != null && result.getAlarmTypeFields() != null ? result.getAlarmTypeFields() : new ArrayList<>());
                return;
            }

            log.info("查询到数据 - 总记录数: {}, 实际返回记录数: {}",
                    result.getTotalRecords(),
                    result.getEngineerScores().size());

            // 3. 准备导出数据
            List<Map<String, Object>> exportData = prepareExportData(result);

            // 4. 生成文件名
            String fileName = generateFileName();

            // 5. 执行导出（使用自定义方法，确保列结构与查询接口一致）
            exportToExcelWithDynamicHeaders(exportData, fileName, result.getAlarmTypeFields());

            log.info("工程师评估评分数据导出成功，文件名: {}, 数据行数: {}", fileName, exportData.size());

        } catch (Exception e) {
            log.error("导出工程师评估评分数据失败", e);
            throw new RuntimeException("Excel 导出失败: " + e.getMessage(), e);
        }
    }

    /**
     * 准备导出数据
     */
    private List<Map<String, Object>> prepareExportData(WorkEvaluationFlatScoreVO result) {
        List<Map<String, Object>> exportData = new ArrayList<>();
        List<String> alarmTypeFields = result.getAlarmTypeFields();

        for (Map<String, Object> engineerScore : result.getEngineerScores()) {
            Map<String, Object> exportRow = new LinkedHashMap<>();

            // 按照指定顺序添加字段
            exportRow.put("姓名", engineerScore.get("姓名"));
            exportRow.put("城市", engineerScore.get("城市"));
            exportRow.put("总工单数", engineerScore.get("总工单数"));
            exportRow.put("总得分", engineerScore.get("总得分"));

            // 添加动态的报警类型字段
            for (String alarmTypeField : alarmTypeFields) {
                exportRow.put(alarmTypeField, engineerScore.getOrDefault(alarmTypeField, 0));
            }

            exportData.add(exportRow);
        }

        return exportData;
    }

    /**
     * 创建空数据但包含表头
     */
    private List<Map<String, Object>> createEmptyDataWithHeaders(WorkEvaluationFlatScoreVO result) {
        List<Map<String, Object>> emptyData = new ArrayList<>();
        Map<String, Object> emptyRow = new LinkedHashMap<>();

        // 基础字段
        emptyRow.put("姓名", "");
        emptyRow.put("城市", "");
        emptyRow.put("总工单数", "");
        emptyRow.put("总得分", "");

        // 如果有报警类型字段，也添加空值
        if (result != null && result.getAlarmTypeFields() != null) {
            for (String alarmTypeField : result.getAlarmTypeFields()) {
                emptyRow.put(alarmTypeField, "");
            }
        }

        emptyData.add(emptyRow);
        return emptyData;
    }

    /**
     * 生成文件名
     */
    private String generateFileName() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd_HHmmss");
        return "工程师评估评分_" + sdf.format(new Date());
    }

    /**
     * 导出到Excel（动态表头，不强制添加合计列）
     * 确保导出的列结构与查询接口返回的数据完全一致
     */
    private void exportToExcelWithDynamicHeaders(List<Map<String, Object>> data, String fileName, List<String> alarmTypeFields) {
        try {
            HttpServletResponse response = HttpContextUtils.getHttpServletResponse();

            if (data == null || data.isEmpty()) {
                log.warn("导出数据为空，创建只包含表头的Excel");
                data = createEmptyRowWithHeaders(alarmTypeFields);
            }

            // 1. 构建表头顺序（与查询接口返回的字段顺序完全一致）
            List<String> headList = buildHeaderList(alarmTypeFields);

            log.info("Excel表头顺序: {}", headList);

            // 2. 构建表头（EasyExcel格式）
            List<List<String>> headTitles = headList.stream()
                    .map(Collections::singletonList)
                    .collect(Collectors.toList());

            // 3. 转换数据（按表头顺序）
            List<List<Object>> dataList = new ArrayList<>();
            for (Map<String, Object> row : data) {
                List<Object> rowData = new ArrayList<>();
                for (String key : headList) {
                    Object value = row.getOrDefault(key, "");
                    // 处理日期格式
                    if (value instanceof Date) {
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        value = sdf.format((Date) value);
                    }
                    rowData.add(value);
                }
                dataList.add(rowData);
            }

            // 4. 设置HTTP响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("UTF-8");
            String encodedFileName = URLUtil.encode(fileName).replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + encodedFileName + ".xlsx");

            // 5. 写入Excel
            EasyExcel.write(response.getOutputStream())
                    .head(headTitles)
                    .sheet("工程师评估评分")
                    .doWrite(dataList);

            response.flushBuffer();

            log.info("Excel导出成功 - 文件名: {}, 表头数: {}, 数据行数: {}",
                    fileName, headList.size(), dataList.size());

        } catch (IOException e) {
            log.error("Excel导出失败", e);
            throw new RuntimeException("Excel 导出失败: " + e.getMessage(), e);
        }
    }

    /**
     * 构建表头列表（确保与查询接口返回的字段顺序一致）
     */
    private List<String> buildHeaderList(List<String> alarmTypeFields) {
        List<String> headList = new ArrayList<>();

        // 基础字段（固定顺序）
        headList.add("姓名");
        headList.add("城市");
        headList.add("总工单数");
        headList.add("总得分");

        // 动态报警类型字段（按排序添加）
        if (alarmTypeFields != null) {
            headList.addAll(alarmTypeFields);
        }

        return headList;
    }

    /**
     * 创建只包含表头的空行数据
     */
    private List<Map<String, Object>> createEmptyRowWithHeaders(List<String> alarmTypeFields) {
        List<Map<String, Object>> emptyData = new ArrayList<>();
        Map<String, Object> emptyRow = new LinkedHashMap<>();

        // 基础字段
        emptyRow.put("姓名", "");
        emptyRow.put("城市", "");
        emptyRow.put("总工单数", "");
        emptyRow.put("总得分", "");

        // 动态报警类型字段
        if (alarmTypeFields != null) {
            for (String alarmTypeField : alarmTypeFields) {
                emptyRow.put(alarmTypeField, "");
            }
        }

        emptyData.add(emptyRow);
        return emptyData;
    }
}
