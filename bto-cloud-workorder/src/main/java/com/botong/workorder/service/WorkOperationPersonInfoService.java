package com.botong.workorder.service;

import com.botong.framework.mybatis.service.BaseService;
import com.botong.workorder.entity.WorkOperationPersonInfoEntity;
import com.botong.workorder.vo.WorkOperationPersonInfoVO;

import java.util.List;

/**
 * 工单运维人员关联表
 *
 * <AUTHOR>
 * @since 2023-08-24 17:34:13
 */
public interface WorkOperationPersonInfoService extends BaseService<WorkOperationPersonInfoEntity> {

    /**
     * 新增
     * @param vo vo
     */
    void save(WorkOperationPersonInfoVO vo);

    /**
     * 修改
     * @param vo vo
     */
    void update(WorkOperationPersonInfoVO vo);

    /**
     * 删除
     * @param idList id集合
     */
    void delete(List<Long> idList);

    /**
     * 根据userId查询工单ids
     * @param userId 用户ids
     * @return 工单ids
     */
    List<Long> getWorkIdsByUser(Long userId);

    /**
     * 根据工单ID删除运维信息
     * @param idList 工单ids
     */
    void deleteByWorkIds(List<Long> idList);

    /**
     * 保存或更新运维人员信息
     * @param workId 工单ID
     * @param repairIds 维修人员IDS
     */
    void saveOrUpdate(Long workId, List<Long> repairIds);

    /**
     * 根据工单ID获取维修人员IDS
     * @param workId 工单ID
     * @return 维修人员IDS
     */
    List<Long> getRepairIdsByWorkId(Long workId);

    /**
     * 根据维修名称获取工单IDS
     */
    List<Long> selectWorkIdsByUsernameLike(String repairUser);

}