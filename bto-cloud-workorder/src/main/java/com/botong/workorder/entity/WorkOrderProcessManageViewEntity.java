package com.botong.workorder.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import java.util.Date;

/**
 * 工单流程视图
 */
@Data
@TableName("work_order_process_manage_view")
public class WorkOrderProcessManageViewEntity {
    @Schema(description = "是否过保：0->在保，1->过保")
    private Integer warrantyStatus;

    /**
     * 工单id
     */
    private Long workId;

    /**
     * 工单状态
     */
    private Integer status;

    /**
     * 电站id
     */
    private String plantId;

    /**
     * 光云告警信息
     */
    private String alarmStr;

    /**
     * 提单人id
     */
    private Long upUserId;

    /**
     * 提单人名称
     */
    private String upUserName;

    /**
     * 提单时间
     */
    private Date upWorkTime;

    /**
     * 工单来源
     */
    private Integer source;

    /**
     * 电站户主名称
     */
    private String plantUserName;

    /**
     * 电站名称
     */
    private String plantName;

    /**
     * 地址
     */
    private String address;

    /**
     * 逆变器id
     */
    private String inverterId;

    /**
     * 电站用户电话
     */
    private String userPhone;

    /**
     * 机构id
     */
    private Integer orgId;

    /**
     * 电站归属机构名称
     */
    private String plantTypeName;

    /**
     * 故障现象
     */
    private Integer alarmInfo;

    /**
     * 故障类型
     */
    private Integer alarmType;

    /**
     * 故障设备
     */
    private Integer alarmDevice;

    /**
     * 质保内容
     */
    private String warrantyContent;

    /**
     * 质保到期日期
     */
    private Date warrantyExpireDate;

    /**
     * 故障时间
     */
    private Date alarmTime;

    /**
     * 故障等级
     */
    private Integer alarmLevel;

    /**
     * 计划修复时间
     */
    private Date preRepairTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 审核意见
     */
    private String examineOpinion;

    /**
     * 评价
     */
    private Integer evaluate;

    /**
     * 审核人id
     */
    private Long examineUserId;

    /**
     * 审核结果
     */
    private Integer auditResult;

    /**
     * 审核时间
     */
    private Date examineTime;

    /**
     * 未修复原因
     */
    private String unrepairedCause;

    /**
     * 维修时间
     */
    private Date repairTime;

    /**
     * 超时
     */
    private String overtime;

    /**
     * 故障原因
     */
    private String alarmCause;

    /**
     * 操作原因
     */
    private String opCause;

    /**
     * 优先级
     */
    private Integer priority;

    /**
     * 维修照片路径
     */
    private String repairPhotoPath;

    /**
     * 故障照片路径
     */
    private String faultPhotoPath;

    /**
     * 是否超时
     */
    private Integer overtimed;

    private Date operationTime;

    /**
     * 国家
     */
    private String country;

    /**
     * 省
     */
    private String province;

    /**
     * 市/州
     */
    private String city;

    /**
     * 县/区
     */
    private String area;

    /**
     * 镇/街道
     */
    private String town;


    /**
     * 维修状态 0：已修复 1 ： 未修复
     */
    private Integer repairStatus;

    /**
     * 备注类型 0:文字 1:文件
     */
    private Integer remarkType;

    /**
     * 工单平台
     */
    private Integer platform;

    private String issuePhoto;

    /**
     * 创建时间时间
     */
    private Date createTime;


    @Schema(description = "是否挂起：0->否  1->是")
    @Range(min = 0, max = 1)
    private Integer suspended;

    @Schema(description = "挂起过期时间")
    private Date suspendExpiration;

    @Schema(description = "电站状态")
    private Integer plantStatus;


    @Schema(description = "是否有同行人：0->否  1->是")
    @Range(min = 0, max = 1)
    private Integer hasCompanion;

    // @Schema(description = "同行人")
    // private List<Long> companion;
}