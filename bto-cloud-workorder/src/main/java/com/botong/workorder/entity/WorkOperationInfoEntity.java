package com.botong.workorder.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.botong.framework.common.utils.DateUtils;
import com.botong.framework.mybatis.entity.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

import java.util.Date;
import java.util.List;

/**
 * 工单待运维表
 *
 * <AUTHOR>
 * @since 1.0.0 2023-08-22
 */
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("work_operation_info")
public class WorkOperationInfoEntity extends BaseEntity {

    /**
     * 工单id
     */
    private Long workId;

    /**
     * 未修复原因
     */
    private String unrepairedCause;

	/**
	* 计划修复时间
	*/
	@JsonFormat(pattern = DateUtils.DATE_PATTERN)
	private Date preRepairTime;

    /**
     * 修复时间
     */
    @JsonFormat(pattern = DateUtils.DATE_PATTERN)
    private Date repairTime;

    /**
     * 超时时间
     */
    private String overtime;

    /**
     * 故障原因
     */
    private Integer alarmCause;

    /**
     * 维修详情
     */
    private String opCause;

    /**
     * 优先级
     */
    private Integer priority;

    /**
     * 维修后照片
     */
    private String repairPhotoPath;

    /**
     * 故障现场照片
     */
    private String faultPhotoPath;


    /**
     * 是否超时 1：是  0：否
     */
    private Integer overtimed;

    /**
     * 故障现象
     */
    private Integer alarmInfo;


    /**
     * 故障设备
     */
    private Integer alarmDevice;

    @Schema(description = "是否挂起：0->否  1->是")
    @Range(min = 0, max = 1)
    private Integer suspended;

    @Schema(description = "挂起过期时间")
    private Date suspendExpiration;

    @Schema(description = "维修状态 0：已修复 1 ： 未修复")
    private Integer repairStatus;

    @Schema(description = "是否有同行人：0->否  1->是")
    @Range(min = 0, max = 1)
    private Integer hasCompanion;

    @Schema(description = "同行人")
    private String companion;

    public WorkOperationInfoEntity(Long workId, Date preRepairTime, Integer overtimed) {
        this.workId = workId;
        this.preRepairTime = preRepairTime;
        this.overtimed = overtimed;
    }
}