-- 测试companion字段解析逻辑的SQL
-- 这个SQL用于验证新的companion字段解析逻辑是否正确

-- 测试数据示例：
-- companion字段可能的值：
-- '[123]' -> 单个repairId: 123
-- '[123,456]' -> 多个repairId: 123, 456
-- '[123,456,789]' -> 多个repairId: 123, 456, 789

-- 测试companion字段解析的子查询
SELECT 
    woi_inner.work_id,
    woi_inner.companion,
    CAST(TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(
        REPLACE(REPLACE(woi_inner.companion, '[', ''), ']', ''), 
        ',', numbers.n
    ), ',', -1)) AS UNSIGNED) AS repair_id
FROM work_operation_info woi_inner
INNER JOIN (
    SELECT 1 n UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5
    UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9 UNION ALL SELECT 10
) numbers ON CHAR_LENGTH(woi_inner.companion) - CHAR_LENGTH(REPLACE(woi_inner.companion, ',', '')) >= numbers.n - 1
WHERE woi_inner.companion IS NOT NULL 
AND woi_inner.companion != '' 
AND woi_inner.companion != '[]'
AND woi_inner.deleted = 0
LIMIT 20;

-- 测试完整的查询逻辑（简化版）
SELECT
    companion_ids.repair_id AS repairId,
    su.real_name AS name,
    wopmv.city AS city,
    wopmv.alarm_type AS alarmType,
    COUNT(*) AS count
FROM work_order_process_manage_view wopmv
INNER JOIN work_operation_info woi ON wopmv.work_id = woi.work_id AND woi.deleted = 0
INNER JOIN (
    SELECT 
        woi_inner.work_id,
        CAST(TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(
            REPLACE(REPLACE(woi_inner.companion, '[', ''), ']', ''), 
            ',', numbers.n
        ), ',', -1)) AS UNSIGNED) AS repair_id
    FROM work_operation_info woi_inner
    INNER JOIN (
        SELECT 1 n UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5
        UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9 UNION ALL SELECT 10
    ) numbers ON CHAR_LENGTH(woi_inner.companion) - CHAR_LENGTH(REPLACE(woi_inner.companion, ',', '')) >= numbers.n - 1
    WHERE woi_inner.companion IS NOT NULL 
    AND woi_inner.companion != '' 
    AND woi_inner.companion != '[]'
    AND woi_inner.deleted = 0
) companion_ids ON wopmv.work_id = companion_ids.work_id
INNER JOIN sys_user su ON companion_ids.repair_id = su.id AND su.deleted = 0 AND su.super_admin = 0
WHERE wopmv.city IS NOT NULL
GROUP BY companion_ids.repair_id, su.real_name, wopmv.city, wopmv.alarm_type
ORDER BY su.real_name, wopmv.alarm_type
LIMIT 10;
