package com.botong.system.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.botong.framework.mybatis.dao.BaseDao;
import com.botong.system.entity.SysUserEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 系统用户
 */
@Mapper
public interface SysUserDao extends BaseDao<SysUserEntity> {

	List<SysUserEntity> getList(Map<String, Object> params);

	SysUserEntity getById(@Param("id") Long id);

	List<SysUserEntity> getRoleUserList(Map<String, Object> params);

	default SysUserEntity getByUsername(String username){
		return this.selectOne(new QueryWrapper<SysUserEntity>().eq("username", username));
	}

	default SysUserEntity getByMobile(String mobile){
		return this.selectOne(new QueryWrapper<SysUserEntity>().eq("mobile", mobile));
	}


	/**
	 * 按工单id获取维修人员id
	 *
	 * @param workId 工作id
	 * @return {@link List }<{@link Long }>
	 * <AUTHOR>
	 * @since 2023-08-23 14:23:10
	 */
	List<Long> getRepairIdByWorkId(@Param("workId") Long workId);


	/**
	 * 通过工作id获得同行人员
	 *
	 * @param workId 工作id
	 * @return {@link String }
	 * <AUTHOR>
	 * @since 2025-03-07 10:01:45
	 */
	String getCompanionByWorkId(@Param("workId") Long workId);
}