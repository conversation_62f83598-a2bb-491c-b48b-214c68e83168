package com.botong.system.api;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.botong.api.module.system.SysUserApi;
import com.botong.api.module.system.vo.OrgWithUserVO;
import com.botong.api.module.system.vo.RepairUserVO;
import com.botong.framework.common.utils.Result;
import com.botong.system.entity.SysUserEntity;
import com.botong.system.entity.SysUserRoleEntity;
import com.botong.system.service.SysUserRoleService;
import com.botong.system.service.SysUserService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 用户管理
 *
 * <AUTHOR>
 * @since 2023/8/22 9:52
 */
@RestController
@AllArgsConstructor
public class SysUserApiImpl implements SysUserApi {
    @Resource
    private SysUserRoleService sysUserRoleService;
    @Resource
    private SysUserService sysUserService;

    @Override
    public Result<List<Long>> exist(List<Long> userIdList, Long roleId) {
        LambdaQueryWrapper<SysUserRoleEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper
                .in(CollUtil.isNotEmpty(userIdList), SysUserRoleEntity::getUserId, userIdList)
                .eq(ObjectUtil.isNotNull(roleId), SysUserRoleEntity::getRoleId, roleId);

        List<Long> existIdList = sysUserRoleService.list(wrapper).stream()
                .map(SysUserRoleEntity::getUserId)
                .collect(Collectors.toList());
        List<Long> nonExistIdList = userIdList.stream()
                .filter(id -> !existIdList.contains(id)).collect(Collectors.toList());
        return Result.ok(nonExistIdList);
    }

    @Override
    public Result<List<RepairUserVO>> repairUser(Long workId) {
        List<RepairUserVO> repairUser = sysUserService.repairUserByWorkId(workId);

        return Result.ok(BeanUtil.copyToList(repairUser, RepairUserVO.class));
    }

    @Override
    public Result<List<RepairUserVO>> companionUser(Long workId) {
        List<RepairUserVO> repairUser = sysUserService.companionUserByWorkId(workId);

        return Result.ok(BeanUtil.copyToList(repairUser, RepairUserVO.class));
    }

    @Override
    public List<RepairUserVO> getInfoByIds(List<Long> userIdList) {
        return sysUserService.getUserByIdList(userIdList, null);
    }

    @Override
    public Result<List<Long>> existByOrg(List<Long> userIdList, Long orgId) {
        List<SysUserEntity> userList = sysUserService.lambdaQuery()
                .eq(ObjectUtil.isNotNull(orgId), SysUserEntity::getOrgId, orgId).list();
        List<Long> existIdList = userList.stream()
                .map(SysUserEntity::getId)
                .collect(Collectors.toList());
        return Result.ok(userIdList.stream()
                .filter(id -> !existIdList.contains(id)).collect(Collectors.toList()));
    }

    @Override
    public Result<List<RepairUserVO>> repairByName(Integer dictValue) {
        List<RepairUserVO> users = sysUserService.repairByName(dictValue);
        return Result.ok(users);
    }

    @Override
    public Result<List<OrgWithUserVO>> getOrgWithUserByWorkId(Long workId) {
        List<com.botong.system.vo.OrgWithUserVO> list = sysUserService.getOrgWithUserByWorkId(workId);
        return Result.ok(BeanUtil.copyToList(list, OrgWithUserVO.class));
    }

    @Override
    public String getUserNameById(Long userId) {
        return sysUserService.getUserNameById(userId);
    }

    @Override
    public Result<Map<Long, String>> getUsernameMapByIds(Set<Long> allRepairIds) {
        return sysUserService.getUsernameMapByIds(allRepairIds);
    }
}
