# 工程师评估评分API修改说明

## 修改概述

修改了 `engineer-scores-flat` API接口的数据来源逻辑，从原来的 `work_operation_person_info` 表改为 `work_operation_info` 表的 `companion` 字段。

## 修改详情

### 原始实现
- **数据来源**：`work_operation_person_info` 表的 `repair_id` 字段
- **关联方式**：直接通过 `work_id` 关联获取 `repair_id`

### 新实现
- **数据来源**：`work_operation_info` 表的 `companion` 字段
- **字段格式**：字符串类型，存储格式为 `[xxx]` 或 `[xxx,xxx]`
- **解析逻辑**：通过SQL解析 `companion` 字段中的 `repairId` 值

## 核心修改

### 1. XML映射文件修改
**文件**：`bto-cloud-workorder/src/main/resources/mapper/workorder/WorkEvaluationScoreDao.xml`

**修改内容**：
- 将原来的 `work_operation_person_info` 表关联改为 `work_operation_info` 表
- 添加子查询解析 `companion` 字段中的 `repairId` 值
- 保持所有查询条件和返回字段不变

### 2. companion字段解析逻辑

```sql
-- 解析companion字段的子查询
SELECT 
    woi_inner.work_id,
    CAST(TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(
        REPLACE(REPLACE(woi_inner.companion, '[', ''), ']', ''), 
        ',', numbers.n
    ), ',', -1)) AS UNSIGNED) AS repair_id
FROM work_operation_info woi_inner
INNER JOIN (
    SELECT 1 n UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5
    UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9 UNION ALL SELECT 10
) numbers ON CHAR_LENGTH(woi_inner.companion) - CHAR_LENGTH(REPLACE(woi_inner.companion, ',', '')) >= numbers.n - 1
WHERE woi_inner.companion IS NOT NULL 
AND woi_inner.companion != '' 
AND woi_inner.companion != '[]'
AND woi_inner.deleted = 0
```

### 3. 解析逻辑说明

1. **去除方括号**：`REPLACE(REPLACE(woi_inner.companion, '[', ''), ']', '')`
2. **分割字符串**：使用 `SUBSTRING_INDEX` 函数按逗号分割
3. **处理多个值**：通过 numbers 表生成序号，支持最多10个repairId
4. **类型转换**：将字符串转换为 UNSIGNED 整数类型
5. **过滤条件**：排除空值、空字符串和空数组 `[]`

## 支持的companion格式

- `[123]` → 解析出 repairId: 123
- `[123,456]` → 解析出 repairId: 123, 456
- `[123,456,789]` → 解析出 repairId: 123, 456, 789
- `[]` → 被过滤，不参与计算
- `null` 或 空字符串 → 被过滤，不参与计算

## API返回数据结构

**保持完全不变**，仍然返回：
```json
{
  "code": 0,
  "data": [
    {
      "姓名": "张三",
      "城市": "北京",
      "总工单数": 10,
      "总得分": 85.5,
      "巡检": 5,
      "纠纷": 3,
      "其他": 2,
      "未统计": 0
    }
  ]
}
```

## 测试建议

1. **功能测试**：验证API返回数据的正确性
2. **性能测试**：由于增加了子查询，需要关注查询性能
3. **边界测试**：测试各种companion字段格式的处理
4. **数据一致性**：对比修改前后的数据结果

## 注意事项

1. **性能影响**：新的SQL查询包含子查询和字符串处理，可能影响查询性能
2. **数据限制**：当前支持最多10个repairId，如需更多可调整numbers表
3. **数据完整性**：确保companion字段中的repairId在sys_user表中存在
4. **向后兼容**：如果companion字段为空，该工单不会出现在评分结果中

## 相关文件

- `WorkEvaluationScoreDao.xml` - 主要修改文件
- `WorkEvaluationScoreController.java` - API入口（无需修改）
- `WorkEvaluationScoreService.java` - 服务层（无需修改）
- `WorkOperationInfoEntity.java` - companion字段定义
