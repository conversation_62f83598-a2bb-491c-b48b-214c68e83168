package com.botong.api.module.system;

import com.botong.api.module.config.FeignTokenConfigure;
import com.botong.api.module.constant.ServerNames;
import com.botong.api.module.system.vo.OrgWithUserVO;
import com.botong.api.module.system.vo.RepairUserVO;
import com.botong.framework.common.utils.Result;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 用户管理feign请求API
 * <AUTHOR>
 * @since 2023/8/22 9:50
 */
@FeignClient(name = ServerNames.SYSTEM_SERVER_NAME, contextId = "sysUserApi", configuration = FeignTokenConfigure.class)
public interface SysUserApi {

    /**
     * 是否存在用户
     *
     * @param userIdList 用户id列表
     * @param roleId     角色id
     * @return {@link Result }<{@link List }<{@link Long }>>
     * <AUTHOR>
     * @since 2023-08-22 16:47:45
     */
    @PostMapping(value = "api/user/exist")
    Result<List<Long>> exist(@NotNull @RequestBody List<Long> userIdList, @RequestParam Long roleId);

    /**
     * 通过维修人员ids获取个人信息
     * @param userIdList 用户ids
     * @return 维修人员信息集合
     */
    @PostMapping(value = "api/user/getInfoByIds")
    List<RepairUserVO> getInfoByIds(@NotNull @RequestBody List<Long> userIdList);

    /**
     * 根据工单id获取维修人员列表
     *
     * @param workId 工作id
     * @return {@link Result }<{@link List }<{@link RepairUserVO }>>
     * <AUTHOR>
     * @since 2023-09-06 10:29:40
     */
    @GetMapping("api/user/repairUserByWorkId")
    @Operation(summary = "根据工单id获取维修人员列表")
    Result<List<RepairUserVO>> repairUser(@RequestParam Long workId);

    /**
     * 根据工单id获取同行人员列表
     *
     * @param workId 工作id
     * @return {@link Result }<{@link List }<{@link RepairUserVO }>>
     * <AUTHOR>
     * @since 2025-03-07 10:05:58
     */
    @GetMapping("api/user/companionUserByWorkId")
    @Operation(summary = "根据工单id获取同行人员列表")
    Result<List<RepairUserVO>> companionUser(@RequestParam Long workId);

    /**
     * 根据机构id和userId判断是否存在用户
     *
     * @param userIdList 用户id列表
     * @param orgId      组织id
     * @return {@link Result }<{@link List }<{@link RepairUserVO }>>
     * <AUTHOR>
     * @since 2023-09-18 16:42:41
     */
    @PostMapping("api/user/existByOrg")
    @Operation(summary = "根据机构id和userId判断是否存在用户")
    Result<List<Long>> existByOrg(@NotNull @RequestBody List<Long> userIdList, @RequestParam Long orgId);

    @GetMapping("api/user/repairByName")
    @Operation(summary = "获取所有维修人员")
    Result<List<RepairUserVO>> repairByName(@RequestParam @NotNull Integer dictValue);

    @GetMapping("api/user/getOrgWithUserByWorkId")
    @Operation(summary = "根据工单id获取机构人员列表")
    Result<List<OrgWithUserVO>> getOrgWithUserByWorkId(@RequestParam Long workId);

    @GetMapping("api/user/getUserNameById")
    @Operation(summary = "根据用户ID获取名称")
    String getUserNameById(@RequestParam Long userId);

    @PostMapping("api/user/getUsernameMapByIds")
    @Operation(summary = "根据用户ID获取名称")
    Result<Map<Long, String>> getUsernameMapByIds(@NotNull @RequestBody Set<Long> allRepairIds);
}